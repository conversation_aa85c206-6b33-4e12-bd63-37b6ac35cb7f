plugins {
	id 'java-library'
	id 'maven-publish'
	id 'signing'
	id 'eclipse-wtp'
}

apply from: '../YadaWeb/loadSharedProperties.gradle'

group = 'net.yadaframework'
version = yada_version

if (!project.hasProperty('repoPath')) {
	ext.repoPath="repoPathMissing"
}

java {
	sourceCompatibility = JavaVersion.VERSION_21
	targetCompatibility = JavaVersion.VERSION_21
	// toolchain {
	// 	languageVersion = JavaLanguageVersion.of(8)
	// }
	// https://docs.gradle.org/6.0/userguide/java_plugin.html#sec:java-extension
	withSourcesJar()
	withJavadocJar() // https://stackoverflow.com/a/75710366/587641
}

tasks.withType(JavaCompile).configureEach {
	// Needed since Spring 6.1: 
	// https://github.com/spring-projects/spring-framework/wiki/Upgrading-to-Spring-Framework-6.x#parameter-name-retention
	options.compilerArgs.add("-parameters")
}

eclipse {
	jdt {
		// sourceCompatibility = 1.8
		// targetCompatibility = 1.8
		// https://stackoverflow.com/a/35302104/587641
		file {
			File dir = file('.settings')
			dir.mkdirs()
			File f = file('.settings/org.eclipse.core.resources.prefs')
			if (!f.exists()) {
				f.write('eclipse.preferences.version=1\n')
				f.append('encoding/<project>=utf-8')
			}
		}
	}
}

compileJava.options.encoding = 'UTF-8'
compileTestJava.options.encoding = 'UTF-8'

repositories {
	mavenCentral()
}

dependencies {
	// Using "api" to keep things simple: no need to repeat the libraries in other projects for compilation
	implementation project(':YadaWeb')

	api 'org.thymeleaf.extras:thymeleaf-extras-springsecurity6:3.1.3.RELEASE'

	api 'com.google.api-client:google-api-client-gson:2.8.1',
			'org.springframework.security:spring-security-web:6.5.3',
			'org.springframework.security:spring-security-config:6.5.3'

	implementation 'jakarta.annotation:jakarta.annotation-api:3.0.0'

	compileOnlyApi 'jakarta.servlet:jakarta.servlet-api:6.1.0'

    // Consumers might need these at runtime when using embedded Tomcat:
	//	'org.apache.tomcat:tomcat-servlet-api:11.0.10',
	//	'org.apache.tomcat.embed:tomcat-embed-core:11.0.10',
			
}

jar {
	manifest {
		attributes("Implementation-Title": "YadaWebSecurity",
			"Implementation-Version": archiveVersion)
	}
}

// Set the yada version in the properties file
processResources {
	doLast {
		ant.replace(file: "${sourceSets.main.output.resourcesDir}/net.yadaframework.yadawebsecurity.properties", token: '@YADA_VERSION@', value: version)
	}
}

javadoc {
	options.encoding = 'UTF-8'
	options.docEncoding = 'UTF-8'
	options.charSet = 'UTF-8'
	failOnError = false
}

// http://stackoverflow.com/a/27853822/587641
// task javadocJar(type: Jar) {
//     classifier = 'javadoc'
//     from javadoc
// } 

// PublishToMavenLocal task
publishing {
	publications {
		yadaWebSecurityLibrary(MavenPublication) {
			artifactId = 'yadawebsecurity'
			from components.java

			versionMapping {
				usage('java-api') {
					fromResolutionOf('runtimeClasspath')
				}
				usage('java-runtime') {
					fromResolutionOf('runtimeClasspath')
				}
			}
			pom {
				name = 'YadaWebSecurity'
				description = 'Yada Framework authentication and authorization component'
				url = 'https://yadaframework.net/en/index.html'
				inceptionYear = '2014'
				packaging = 'jar'
				licenses {
					license {
						name = 'MIT License'
						url = 'https://en.wikipedia.org/wiki/MIT_License'
					}
				}
				developers {
					developer {
						id = 'xtianus'
						name = 'Studio Ghezzi'
						email = '<EMAIL>'
					}
				}
				organization {
					name = 'Studio Ghezzi'
					url = 'https://studio.ghezzi.net/'
				}
				scm {
					connection = 'scm:git:**************:xtianus/yadaframework.git'
					developerConnection = 'scm:git:**************:xtianus/yadaframework.git'
					url = 'https://github.com/xtianus/yadaframework'
				}
			}
		}
	}
	repositories {
		// Creates task publishYadaWebSecurityLibraryPublicationToPublicRepoRepository
		maven {
			name "PublicRepo"
			def snapshotsRepoUrl = "https://oss.sonatype.org/content/repositories/snapshots/"
			def releasesRepoUrl = "https://oss.sonatype.org/service/local/staging/deploy/maven2/"
			url = version.endsWith('SNAPSHOT') ? snapshotsRepoUrl : releasesRepoUrl
			credentials(PasswordCredentials) {
				username = project.hasProperty('ossrhUsername')?ossrhUsername:'USERUNSET'
				password = project.hasProperty('ossrhPassword')?ossrhPassword:'PWDUNSET'
			}
			// url = "file://${buildDir}/repotest"
		}
		 // Creates task publishYadaWebSecurityLibraryPublicationToLocalRepoRepository
		maven {
			name "LocalRepo"
			// repoPath must be passed when invoking gradle using -P
			url = "file://${repoPath}"
		}    
	}    
}

signing {
	sign publishing.publications.yadaWebSecurityLibrary
}

