plugins {
	id 'java-library'
	id 'maven-publish'
	id 'signing'
	id 'eclipse-wtp'
}

apply from: '../YadaWeb/loadSharedProperties.gradle'

group = 'net.yadaframework'
version = yada_version

if (!project.hasProperty('repoPath')) {
	ext.repoPath="repoPathMissing"
}

java {
    sourceCompatibility = JavaVersion.VERSION_21
    targetCompatibility = JavaVersion.VERSION_21
	// toolchain {
	// 	languageVersion = JavaLanguageVersion.of(8)
	// }
	// https://docs.gradle.org/6.0/userguide/java_plugin.html#sec:java-extension
	withSourcesJar()
    withJavadocJar() // https://stackoverflow.com/a/75710366/587641
}

tasks.withType(JavaCompile).configureEach {
    // Needed since Spring 6.1: 
    // https://github.com/spring-projects/spring-framework/wiki/Upgrading-to-Spring-Framework-6.x#parameter-name-retention
    options.compilerArgs.add("-parameters")
}

eclipse {
	jdt {
		// sourceCompatibility = 1.8
		// targetCompatibility = 1.8
		// https://stackoverflow.com/a/35302104/587641
		file {
      		File dir = file('.settings')
      		dir.mkdirs()
      		File f = file('.settings/org.eclipse.core.resources.prefs')
      		if (!f.exists()) {
      			f.write('eclipse.preferences.version=1\n')
      			f.append('encoding/<project>=utf-8')
      		}
    	}
	}
}

compileJava.options.encoding = 'UTF-8'
compileTestJava.options.encoding = 'UTF-8'

repositories {
    mavenCentral()
}

dependencies {
	// Using "api" to keep things simple: no need to repeat the libraries in other projects for compilation
		
	implementation project(':YadaWeb'), project(':YadaWebSecurity'), 
		// 'org.springframework:spring-webmvc:5.3.23',
		// 'org.springframework:spring-orm:5.3.23',
		// 'org.hibernate:hibernate-entitymanager:5.5.0.Final',
        'com.fasterxml.jackson.core:jackson-annotations:2.19.2',
        'com.fasterxml.jackson.core:jackson-core:2.19.2'
		// 'com.fasterxml.jackson.core:jackson-databind:2.13.4',
		// 'org.apache.commons:commons-io:1.3.2'
		
	testImplementation platform('org.junit:junit-bom:5.13.4')
	testImplementation 'org.junit.jupiter:junit-jupiter'
}

jar {
    manifest {
        attributes("Implementation-Title": "YadaWebCMS",
                   "Implementation-Version": archiveVersion)
    }
}

// Set the yada version in the properties file
processResources {
	doLast {
		ant.replace(file: "${sourceSets.main.output.resourcesDir}/net.yadaframework.yadawebcms.properties", token: '@YADA_VERSION@', value: version)
	}
}

javadoc {
    options.encoding = 'UTF-8'
    options.docEncoding = 'UTF-8'
    options.charSet = 'UTF-8'
    failOnError = false
}

// http://stackoverflow.com/a/27853822/587641
// task javadocJar(type: Jar) {
//     classifier = 'javadoc'
//     from javadoc
// }   

publishing {
    publications {
        yadaWebCMSLibrary(MavenPublication) {
            artifactId = 'yadawebcms'
            from components.java

            versionMapping {
                usage('java-api') {
                    fromResolutionOf('runtimeClasspath')
                }
                usage('java-runtime') {
                    fromResolutionOf('runtimeClasspath')
                }
            }
  			pom {
			    name = 'YadaWebCMS'
			    description = 'Yada Framework for creating Content Management Systems'
			    url = 'https://yadaframework.net/en/index.html'
			    inceptionYear = '2014'
			    packaging = 'jar'
			    licenses {
			        license {
			            name = 'MIT License'
			            url = 'https://en.wikipedia.org/wiki/MIT_License'
			        }
			    }
			    developers {
			        developer {
			            id = 'xtianus'
			            name = 'Studio Ghezzi'
			            email = '<EMAIL>'
			        }
			    }
			    organization {
			    	name = 'Studio Ghezzi'
			    	url = 'https://studio.ghezzi.net/'
			    }
			    scm {
			        connection = 'scm:git:**************:xtianus/yadaframework.git'
			        developerConnection = 'scm:git:**************:xtianus/yadaframework.git'
			        url = 'https://github.com/xtianus/yadaframework'
			    }
			}
        }
    }
    repositories {
    	// Creates task publishYadaWebCMSLibraryPublicationToPublicRepoRepository
        maven {
        	name "PublicRepo"
            def snapshotsRepoUrl = "https://oss.sonatype.org/content/repositories/snapshots/"
            def releasesRepoUrl = "https://oss.sonatype.org/service/local/staging/deploy/maven2/"
			url = version.endsWith('SNAPSHOT') ? snapshotsRepoUrl : releasesRepoUrl
			credentials(PasswordCredentials) {
            	username = project.hasProperty('ossrhUsername')?ossrhUsername:'USERUNSET'
            	password = project.hasProperty('ossrhPassword')?ossrhPassword:'PWDUNSET'
			}
        }
         // Creates task publishYadaWebCMSLibraryPublicationToLocalRepoRepository
        maven {
        	name "LocalRepo"
        	// repoPath must be passed when invoking gradle using -P
        	url = "file://${repoPath}"
    	}
    }    
}

signing {
    sign publishing.publications.yadaWebCMSLibrary
}


